--- get_tree_by_creator.lua
-- KEYS[1]: userId
--
-- Fetches all devices created by a user, and all their ancestor nodes.
--
-- Returns: A JSON array string of all unique nodes found.

local userId = KEYS[1]
if not userId or userId == '' then
    return '[]'
end

local currentVersionKey = "tree:current_version"
local currentVersion = redis.call('GET', currentVersionKey)
if not currentVersion then
    currentVersion = "v1" -- 默认版本
end
local nodeKeyPrefix = "tree:" .. currentVersion .. ":node:"

local creator_index_key = "tree:" .. currentVersion .. ":user_devices:" .. userId

-- 1. Get all device IDs created by the user
local deviceIds = redis.call("SMEMBERS", creator_index_key)
if #deviceIds == 0 then
    return '[]'
end

local seen_ids = {}
local nodes_to_return = {}

-- 2. For each device, traverse up to the root and collect all nodes
for _, deviceId in ipairs(deviceIds) do
    local current_id = deviceId
    while current_id and current_id ~= '' and current_id ~= 'null' and not seen_ids[current_id] do
        seen_ids[current_id] = true
        local node_key = node_key_prefix .. current_id
        local node_data_flat = redis.call("HGETALL", node_key)

        if #node_data_flat > 0 then
            local node = {}
            for i = 1, #node_data_flat, 2 do
                node[node_data_flat[i]] = node_data_flat[i + 1]
            end
            table.insert(nodes_to_return, node)
            current_id = node['parentId']
        else
            -- Node data not found, stop this path
            current_id = nil
        end
    end
end

return cjson.encode(nodes_to_return)
