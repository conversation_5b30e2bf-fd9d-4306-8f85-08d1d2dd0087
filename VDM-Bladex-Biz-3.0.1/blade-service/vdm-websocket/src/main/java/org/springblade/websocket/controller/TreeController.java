package org.springblade.websocket.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.websocket.dto.TreeSearchData;
import org.springblade.websocket.dto.tree.BaseNode;
import org.springblade.websocket.dto.tree.TreeExpandRequest;
import org.springblade.websocket.service.TreeService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/tree")
@AllArgsConstructor
@Slf4j
@Api(value = "部门终端树缓存", tags = "部门终端树缓存接口")
public class TreeController {

	private final TreeService treeService;

	/**
	 * 动态获取树结构（基于展开节点）
	 */
	@PostMapping("/dynamicTree")
	@ApiOperation("根据展开节点动态获取树结构")
	public R<List<BaseNode>> getDynamicTree(@RequestBody TreeExpandRequest request, BladeUser user) {
		// 参数验证
		if (request == null || request.getExpandedNodeIds() == null || request.getExpandedNodeIds().isEmpty()) {
			return R.fail("展开节点ID列表不能为空");
		}

		try {
			List<BaseNode> treeData = treeService.getDynamicTree(request.getExpandedNodeIds(), user);
			return R.data(treeData);
		} catch (Exception e) {
			log.error("动态获取树结构失败", e);
			return R.fail("获取树结构失败：" + e.getMessage());
		}
	}


	@GetMapping("/search")
	public R<Page<TreeSearchData>> searchNodes(
		@RequestParam String keyword,
		@RequestParam(defaultValue = "1") long current,
		@RequestParam(defaultValue = "10") long pageSize,
		BladeUser user
	) {

		return R.data(treeService.searchNodes(keyword, current, pageSize));
	}



	@GetMapping("/by-creator")
	public R<List<BaseNode>> getTreeByCreator(
		@ApiParam(value = "用户ID", required = true) @RequestParam String userId) {
		return R.data(treeService.getTreeByCreator(userId));
	}

	@PostMapping("/buildTree")
	public R<Void> buildTree() {
		treeService.buildTree(1);
		return R.success("缓存构建任务已启动");
	}


}
